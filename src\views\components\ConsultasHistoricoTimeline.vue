<template>
  <div class="consultas-historico-container">
    <!-- Botão para registrar histórico -->
    <div class="header-actions mb-3">
      <button
        class="btn btn-primary btn-sm"
        @click="abrirModalRegistrarHistorico"
        title="Registrar novo histórico"
      >
        <font-awesome-icon :icon="['fas', 'plus']" class="me-2" />
        Registrar Histórico
      </button>
    </div>

    <div v-if="isLoading" class="w-100 text-center py-3">
      <div class="spinner-border text-primary" role="status"></div>
      <p class="mt-2">Carregando dados do paciente...</p>
    </div>

    <div v-else-if="!itensTimeline.length" class="empty-state">
      <div class="empty-state-message">
        <div class="icon-wrapper">
          <font-awesome-icon :icon="['fas', 'calendar-times']" class="empty-state-icon" />
        </div>
        <p>Não há consultas ou histórico registrados para este paciente.</p>
        <p class="text-muted small">Clique em "Registrar Histórico" para adicionar o primeiro registro.</p>
      </div>
    </div>

    <div v-else class="timeline-container">
      <!-- Item futuro: O que fazer na próxima consulta -->
      <div v-if="proximaConsultaInfo" class="timeline-item futuro-item">
        <div class="timeline-badge futuro-badge">
          <font-awesome-icon :icon="['fas', 'arrow-right']" />
        </div>
        <div class="timeline-panel futuro-panel">
          <div class="timeline-heading">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="timeline-title">
                O que fazer na próxima consulta
                <span class="futuro-indicator">Próximo</span>
              </h6>
            </div>
            <p class="timeline-date">
              <small>Baseado na última consulta realizada</small>
            </p>
          </div>
          <div class="timeline-body">
            <p>{{ proximaConsultaInfo }}</p>
          </div>
        </div>
      </div>

      <!-- Timeline items -->
      <div v-for="(item, index) in itensTimeline" :key="index" class="timeline-item">
        <div class="timeline-badge" :class="getTimelineBadgeClass(item)">
          <font-awesome-icon :icon="['fas', getTimelineIcon(item)]" />
        </div>
        <div class="timeline-panel" :class="getTimelinePanelClass(item)">
          <!-- Consulta com layout compacto tipo tabela -->
          <div v-if="item.tipo === 'consulta'" class="consulta-compacta">
            <div class="consulta-header">
              <div class="consulta-info-principal">
                <div class="data-titulo">
                  <h6 class="timeline-title mb-0">
                    Consulta -
                    <span class="ortodontista-nome-inline">{{ getDentistaName(item.dentista_id) }}</span>
                    <span class="categoria-badge-inline" :class="getCategoriaClass(item.categoria)">
                      <font-awesome-icon :icon="['fas', getCategoriaIcon(item.categoria)]" class="me-1" />
                      {{ getCategoriaNome(item.categoria) }}
                    </span>
                  </h6>
                  <p class="timeline-date mb-0">
                    <small><strong>{{ $filters.dateDmy(item.data) }} às {{ formatTime(item.horario) }}</strong></small>
                    <span class="ms-2 tempo-passado">{{ $filters.howMuchTime(item.data + ' ' + item.horario, { type: 'date' }) }}</span>
                  </p>

                  <!-- Histórico da consulta e próxima consulta lado a lado - logo abaixo da timestamp -->
                  <div v-if="item.historico_consulta || item.proxima_consulta" class="consulta-historico-lado-a-lado">
                    <div v-if="item.historico_consulta" class="historico-coluna">
                      <div class="historico-label-editavel" @click="editarHistoricoConsulta(item.consulta_id)" title="Clique para editar o histórico">
                        <font-awesome-icon :icon="['fas', 'clipboard-list']" class="me-1" />
                        Histórico
                        <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon" />
                      </div>
                      <p class="historico-texto-editavel" @click="editarHistoricoConsulta(item.consulta_id)">{{ item.historico_consulta }}</p>
                    </div>

                    <div v-if="item.proxima_consulta" class="proxima-coluna">
                      <div class="historico-label-editavel proxima-label" @click="editarProximaConsulta(item.consulta_id)" title="Clique para editar o que fazer na próxima consulta">
                        <font-awesome-icon :icon="['fas', 'arrow-right']" class="me-1" />
                        Próxima consulta
                        <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon" />
                      </div>
                      <p class="historico-texto-editavel proxima-texto" @click="editarProximaConsulta(item.consulta_id)">{{ item.proxima_consulta }}</p>
                    </div>
                  </div>
                </div>
                <div class="consulta-actions">
                  <button
                    class="btn btn-sm btn-outline-primary"
                    @click.stop="editarConsulta(item.consulta_id)"
                    title="Editar consulta"
                  >
                    <font-awesome-icon :icon="['fas', 'edit']" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Histórico manual -->
          <div v-else class="historico-manual">
            <div class="historico-header">
              <h6 class="timeline-title mb-0">{{ getTimelineTitle(item) }}</h6>
              <p class="timeline-date mb-0">
                <small><strong>{{ $filters.dateDmy(item.data) }} às {{ formatTime(item.horario) }}</strong></small>
                <span class="ms-2 tempo-passado">{{ $filters.howMuchTime(item.data + ' ' + item.horario, { type: 'date' }) }}</span>
              </p>
            </div>
            <p class="historico-descricao">{{ item.descricao }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para registrar histórico -->
    <RegistrarHistoricoModal ref="registrarHistoricoModal" @historico-salvo="recarregarDados" />
  </div>
</template>

<style scoped>
.consultas-historico-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2.5rem 2rem;
  text-align: center;
  background: #fff;
  border-radius: 16px;
  margin: 0.5rem 0;
  border: 2px dashed #e2e8f0;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.empty-state-message:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.empty-state-message .icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.empty-state-message .empty-state-icon {
  font-size: 2rem;
  color: #fff;
}

.empty-state-message p {
  color: #64748b;
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  line-height: 1.5;
}

/* Header actions */
.header-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.header-actions .btn {
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease;
}

.header-actions .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Timeline styles */
.timeline-container {
  position: relative;
  padding: 10px 0;
  max-width: 1200px;
  margin: 0 auto;
}

.timeline-container:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 15px;
  width: 2px;
  background: linear-gradient(to bottom, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.6));
  border-radius: 1px;
}

.timeline-item {
  position: relative;
  margin-bottom: 12px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.timeline-badge {
  position: absolute;
  top: 8px;
  left: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
  font-size: 0.8em;
  line-height: 30px;
  background-color: white;
  border: 2px solid #007bff;
  color: #007bff;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
}

.timeline-panel {
  position: relative;
  margin-left: 50px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.timeline-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1dce8;
}

.timeline-panel:before {
  content: '';
  position: absolute;
  top: 12px;
  left: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #e9ecef;
}

.timeline-panel:after {
  content: '';
  position: absolute;
  top: 12px;
  left: -7px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #fff;
}

.timeline-title {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 0.95rem;
}

.timeline-date {
  color: #6c757d;
  margin: 0;
  font-size: 0.8rem;
}

.tempo-passado {
  color: #28a745;
  font-weight: 500;
}

/* Consulta styles */
.consulta-badge {
  border-color: #28a745;
  color: #28a745;
  box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
}

.consulta-panel {
  border-left: 3px solid #28a745;
}

.consulta-panel:before {
  border-right-color: #28a745;
}

/* Layout compacto para consultas */
.consulta-compacta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.consulta-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.consulta-info-principal {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.data-titulo {
  flex: 1;
}

.consulta-actions {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
}

/* Ortodontista inline no título */
.ortodontista-nome-inline {
  font-size: 0.85rem;
  color: #6c757d;
  font-weight: 500;
  margin: 0 6px;
}

/* Categoria inline no título */
.categoria-badge-inline {
  display: inline-flex;
  align-items: center;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  margin-left: 4px;
}

.categoria-badge-inline:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.categoria-badge-inline.categoria-badge-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-secondary {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: #fff;
}

.categoria-badge-inline.categoria-badge-dark {
  background: linear-gradient(135deg, #343a40, #1d2124);
  color: #fff;
}

/* Histórico lado a lado */
.consulta-historico-lado-a-lado {
  margin-top: 6px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.historico-coluna,
.proxima-coluna {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.historico-label-editavel {
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 2px 4px;
  border-radius: 4px;
  position: relative;
}

.historico-label-editavel:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.historico-label-editavel .edit-icon {
  opacity: 0;
  margin-left: auto;
  font-size: 0.7rem;
  transition: opacity 0.2s ease;
}

.historico-label-editavel:hover .edit-icon {
  opacity: 1;
}

.historico-texto-editavel {
  font-size: 0.85rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #dee2e6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.historico-texto-editavel:hover {
  background-color: #e9ecef;
  border-left-color: #007bff;
}

.proxima-coluna .historico-label-editavel.proxima-label {
  color: #ffc107;
}

.proxima-coluna .historico-label-editavel.proxima-label:hover {
  color: #e0a800;
}

.proxima-coluna .historico-texto-editavel.proxima-texto {
  background-color: #fff9e6;
  border-left-color: #ffc107;
}

.proxima-coluna .historico-texto-editavel.proxima-texto:hover {
  background-color: #fff3cd;
  border-left-color: #e0a800;
}

/* Histórico manual */
.historico-manual {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.historico-header {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.historico-descricao {
  font-size: 0.9rem;
  color: #495057;
  line-height: 1.5;
  margin: 0;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007bff;
}

/* Categoria badges */
.categoria-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.65rem;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.2px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.categoria-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.categoria-badge-info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: #fff;
}

.categoria-badge-success {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: #fff;
}

.categoria-badge-danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: #fff;
}

.categoria-badge-secondary {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: #fff;
}

.categoria-badge-dark {
  background: linear-gradient(135deg, #343a40, #1d2124);
  color: #fff;
}

/* Valor badge */
.valor-badge {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  padding: 0.3rem 0.7rem;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  color: #495057;
  border-radius: 10px;
  font-weight: 700;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.valor-badge:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Status badges */
.badge {
  font-size: 0.75rem;
  padding: 0.5rem 1rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Futuro item styles */
.futuro-item {
  position: relative;
  margin-bottom: 20px;
  animation: fadeIn 0.5s ease-out;
}

.futuro-badge {
  border-color: #ffc107;
  color: #ffc107;
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.3);
  background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.futuro-panel {
  border-left: 3px solid #ffc107;
  background: linear-gradient(135deg, #fff9e6, #fffbf0);
  border: 1px solid #ffeaa7;
}

.futuro-panel:before {
  border-right-color: #ffeaa7;
}

.futuro-panel:after {
  border-right-color: #fff9e6;
}

.futuro-indicator {
  display: inline-block;
  font-size: 0.65rem;
  font-weight: 500;
  color: #fff;
  background-color: #ffc107;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  text-transform: uppercase;
  vertical-align: middle;
  letter-spacing: 0.5px;
}

/* Consulta histórico styles */
.consulta-historico {
  border-top: 1px solid #e9ecef;
  padding-top: 1rem;
  margin-top: 1rem;
}

.historico-secao {
  margin-bottom: 0;
}

.historico-secao-titulo {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.historico-secao-conteudo {
  margin-bottom: 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.5;
  padding-left: 24px;
}

.proxima-consulta-titulo {
  color: #ffc107;
}

.proxima-consulta-conteudo {
  background-color: #fff9e6;
  border-left: 3px solid #ffc107;
  padding: 8px 12px;
  border-radius: 4px;
  margin-left: 0;
  padding-left: 36px;
}

/* Action buttons */
.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

/* Loading spinner styling */
.spinner-border {
  color: #667eea;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .timeline-container {
    padding: 5px 0;
  }

  .timeline-panel {
    margin-left: 40px;
    padding: 10px 12px;
  }

  .timeline-badge {
    width: 26px;
    height: 26px;
    line-height: 26px;
    font-size: 0.75em;
    top: 6px;
  }

  .timeline-container:before {
    left: 12px;
  }

  .timeline-panel:before {
    top: 10px;
    left: -6px;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-right: 6px solid #e9ecef;
  }

  .timeline-panel:after {
    top: 10px;
    left: -5px;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-right: 6px solid #fff;
  }

  .consulta-info-principal {
    flex-direction: column;
    gap: 6px;
  }

  .consulta-actions {
    align-self: flex-end;
  }

  .consulta-historico-lado-a-lado {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .categoria-badge-inline {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }

  .categoria-badge {
    font-size: 0.6rem;
    padding: 0.25rem 0.5rem;
  }

  .valor-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.6rem;
  }

  .btn-sm {
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
  }

  .historico-texto {
    padding-left: 16px;
    font-size: 0.8rem;
  }

  .historico-label {
    font-size: 0.75rem;
  }

  .timeline-title {
    font-size: 0.9rem;
  }

  .timeline-date {
    font-size: 0.75rem;
  }
}
</style>

<script>
import moment from 'moment';
import { getHistoricosPaciente } from "@/services/historicoPacienteService";
import { getConsultasByPaciente } from "@/services/consultasService";
import RegistrarHistoricoModal from "@/components/RegistrarHistoricoModal.vue";
import cSwal from "@/utils/cSwal.js";

export default {
  name: "ConsultasHistoricoTimeline",
  components: {
    RegistrarHistoricoModal
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    dentistas: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:consultas', 'editar-consulta', 'ver-historico'],
  data() {
    return {
      isLoading: false,
      historicos: [],
      consultas: [],
      proximaConsultaInfo: null,
      categorias: [
        { valor: 'acompanhamento', nome: 'Acompanhamento/ativação', cor: 'info', icone: 'wrench' },
        { valor: 'primeira_consulta', nome: 'Primeira consulta', cor: 'success', icone: 'clipboard-check' },
        { valor: 'emergencia', nome: 'Emergência', cor: 'danger', icone: 'exclamation-triangle' },
        { valor: 'montagem', nome: 'Montagem', cor: 'secondary', icone: 'tools' },
        { valor: 'remocao', nome: 'Remoção', cor: 'secondary', icone: 'minus-circle' },
        { valor: 'replanejamento', nome: 'Replanejamento', cor: 'dark', icone: 'sync-alt' },
        { valor: 'pos_tratamento', nome: 'Pós-tratamento', cor: 'secondary', icone: 'check-double' }
      ]
    };
  },
  computed: {
    itensTimeline() {
      const items = [];

      // Adicionar consultas
      this.consultas.forEach(consulta => {
        const item = {
          id: `consulta_${consulta.id}`,
          data: consulta.horario.split(' ')[0],
          horario: consulta.horario.split(' ')[1] || '00:00:00',
          tipo: 'consulta',
          consulta_id: consulta.id,
          categoria: consulta.categoria,
          dentista_id: consulta.dentista_id,
          valor: consulta.valor,
          status: consulta.status,
          historico_consulta: '',
          proxima_consulta: ''
        };

        // Buscar histórico da consulta nos históricos
        const historicoConsulta = this.historicos.find(h => 
          h.codigo_acao === 'alteracao_consulta' && 
          h.consulta_id === consulta.id
        );

        if (historicoConsulta && historicoConsulta.modificacoes) {
          try {
            let modificacoes = typeof historicoConsulta.modificacoes === 'string'
              ? JSON.parse(historicoConsulta.modificacoes)
              : historicoConsulta.modificacoes;

            if (Array.isArray(modificacoes)) {
              const historicoMod = modificacoes.find(m => m.titulo === 'Histórico da consulta');
              const proximaMod = modificacoes.find(m => m.titulo === 'O que fazer na próxima consulta');
              
              if (historicoMod && historicoMod.descricao) {
                item.historico_consulta = historicoMod.descricao;
              }
              if (proximaMod && proximaMod.descricao) {
                item.proxima_consulta = proximaMod.descricao;
              }
            }
          } catch (e) {
            console.error('Erro ao processar modificações:', e);
          }
        }

        items.push(item);
      });

      // Adicionar históricos manuais
      this.historicos.forEach(historico => {
        if (historico.codigo_acao !== 'alteracao_consulta') {
          items.push({
            ...historico,
            tipo: 'historico_manual'
          });
        }
      });

      // Ordenar por data/horário (mais recente primeiro)
      return items.sort((a, b) => {
        const dateA = new Date(`${a.data} ${a.horario}`);
        const dateB = new Date(`${b.data} ${b.horario}`);
        return dateB - dateA;
      });
    }
  },
  mounted() {
    this.fetchConsultas();
  },
  watch: {
    'paciente.id': {
      handler(newVal) {
        if (newVal) {
          this.fetchConsultas();
        }
      },
      immediate: true
    }
  },
  methods: {
    async fetchConsultas() {
      if (!this.paciente || !this.paciente.id) return;

      this.isLoading = true;
      try {
        // Carregar consultas
        const consultas = await getConsultasByPaciente(this.paciente.id);
        if (consultas && Array.isArray(consultas)) {
          this.consultas = consultas;
          this.$emit('update:consultas', consultas);
        }

        // Carregar históricos
        const historicos = await getHistoricosPaciente(this.paciente.id);
        if (historicos && Array.isArray(historicos)) {
          this.historicos = historicos;
        }

        // Buscar informação da próxima consulta
        await this.buscarProximaConsultaInfo();
      } catch (error) {
        console.error('Erro ao buscar dados do paciente:', error);
        cSwal.cError("Erro ao carregar dados do paciente.");
      } finally {
        this.isLoading = false;
      }
    },
    async buscarProximaConsultaInfo() {
      // Buscar a orientação mais recente para próxima consulta nos históricos já carregados
      const orientacoesProximaConsulta = this.historicos
        .filter(h => h.codigo_acao === 'alteracao_consulta' && h.modificacoes)
        .map(h => {
          try {
            let modificacoes = typeof h.modificacoes === 'string'
              ? JSON.parse(h.modificacoes)
              : h.modificacoes;

            if (Array.isArray(modificacoes)) {
              const proximaConsulta = modificacoes.find(m => m.titulo === "O que fazer na próxima consulta");
              if (proximaConsulta && proximaConsulta.descricao && proximaConsulta.descricao.trim()) {
                return {
                  descricao: proximaConsulta.descricao,
                  data: h.data,
                  horario: h.horario
                };
              }
            }
          } catch (e) {
            console.error('Erro ao processar modificações:', e);
          }
          return null;
        })
        .filter(item => item !== null)
        .sort((a, b) => {
          const dateA = new Date(`${a.data} ${a.horario}`);
          const dateB = new Date(`${b.data} ${b.horario}`);
          return dateB - dateA; // Mais recente primeiro
        });

      if (orientacoesProximaConsulta.length > 0) {
        this.proximaConsultaInfo = orientacoesProximaConsulta[0].descricao;
      }
    },
    formatTime(dateTime) {
      if (!dateTime) return '-';

      try {
        // Se for uma string de data completa, extrair apenas a parte da hora
        if (typeof dateTime === 'string') {
          // Verificar se é uma data completa (YYYY-MM-DD HH:MM:SS)
          if (dateTime.includes('T') || dateTime.includes(' ')) {
            const date = new Date(dateTime);
            if (!isNaN(date.getTime())) {
              return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            }
          }

          // Se for apenas um horário (HH:MM:SS)
          if (dateTime.includes(':')) {
            const timeParts = dateTime.split(':');
            if (timeParts.length >= 2) {
              return `${timeParts[0]}:${timeParts[1]}`;
            }
          }
        }

        // Se for um objeto Date
        if (dateTime instanceof Date) {
          return dateTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }

        // Tentar com moment como último recurso
        return moment(dateTime).format('HH:mm');
      } catch (error) {
        console.error('Erro ao formatar horário:', error);
        return '-';
      }
    },
    getTimelineTitle(item) {
      if (item.tipo === 'consulta') {
        return `Consulta - ${this.getCategoriaNome(item.categoria)}`;
      }

      switch (item.codigo_acao) {
        case 'registro_manual':
          return 'Registro de histórico';
        case 'inicio_tratamento':
          return 'Início do tratamento';
        case 'alteracao_aparelho':
          return 'Alteração de aparelho';
        case 'ajuste_aparelho':
          return 'Ajuste de aparelho';
        case 'remocao_aparelho':
          return 'Remoção de aparelho';
        default:
          return item.codigo_acao ? item.codigo_acao : 'Registro de histórico';
      }
    },
    getTimelineIcon(item) {
      if (item.tipo === 'consulta') {
        return 'calendar-check';
      }

      switch (item.codigo_acao) {
        case 'registro_manual':
          return 'edit';
        case 'inicio_tratamento':
          return 'play-circle';
        case 'alteracao_aparelho':
        case 'ajuste_aparelho':
          return 'wrench';
        case 'remocao_aparelho':
          return 'minus-circle';
        default:
          return 'circle';
      }
    },
    getTimelineBadgeClass(item) {
      if (item.tipo === 'consulta') {
        return 'consulta-badge';
      }
      return '';
    },
    getTimelinePanelClass(item) {
      if (item.tipo === 'consulta') {
        return 'consulta-panel';
      }
      return '';
    },
    getDentistaName(dentistaId) {
      if (!dentistaId) return '-';

      const dentista = this.dentistas.find(d => d.id === dentistaId || d.id === parseInt(dentistaId));
      return dentista ? dentista.nome : `Dentista #${dentistaId}`;
    },
    formatCurrency(value) {
      if (!value && value !== 0) return '-';

      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },
    getStatusClass(status) {
      const statusClasses = {
        'agendada': 'bg-info',
        'realizada': 'bg-success',
        'cancelada': 'bg-danger',
        'reagendada': 'bg-warning'
      };

      return statusClasses[status] || 'bg-secondary';
    },
    getStatusText(status) {
      const statusTexts = {
        'agendada': 'AGENDADA',
        'realizada': 'REALIZADA',
        'cancelada': 'CANCELADA',
        'reagendada': 'REAGENDADA'
      };

      return statusTexts[status] || status.toUpperCase();
    },
    getCategoriaData(categoria) {
      return this.categorias.find(cat => cat.valor === categoria) ||
             { valor: categoria, nome: categoria, cor: 'secondary', icone: 'question' };
    },
    getCategoriaNome(categoria) {
      if (!categoria) return '-';
      return this.getCategoriaData(categoria).nome;
    },
    getCategoriaIcon(categoria) {
      if (!categoria) return 'question';
      return this.getCategoriaData(categoria).icone;
    },
    getCategoriaClass(categoria) {
      if (!categoria) return 'categoria-badge-secondary';
      const cor = this.getCategoriaData(categoria).cor;
      return `categoria-badge-${cor}`;
    },
    editarConsulta(id) {
      this.$emit('editar-consulta', id);
    },
    verHistorico(id) {
      this.$emit('ver-historico', id);
    },
    editarHistoricoConsulta(id) {
      this.$emit('ver-historico', id);
    },
    editarProximaConsulta(id) {
      this.$emit('ver-historico', id);
    },
    abrirModalRegistrarHistorico() {
      this.$refs.registrarHistoricoModal.abrirModal(this.paciente.id);
    },
    recarregarDados() {
      this.fetchConsultas();
    }
  }
};
</script>
