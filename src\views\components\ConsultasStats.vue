<template>
  <div class="consultas-stats mb-3 container-fluid py-1" v-if="!isLoading">
    <div class="row">
      <!-- 1 - <PERSON><PERSON><PERSON> do tratamento -->
      <div class="col-6 col-md mb-2 mb-md-0">
        <div class="stats-card">
          <div class="stats-title">
            <font-awesome-icon :icon="['fas', 'play']" class="me-1" />
            Início do tratamento
          </div>
          <div class="date-badge-container">
            <div class="date-badge">
              {{ this.$filters.dateDmy(paciente.data_inicio_tratamento) }}
            </div>
            <div class="edit-icon" @click="editarDataInicio">
              <font-awesome-icon :icon="['fas', 'edit']" />
            </div>
          </div>
          <div class="text-sm">{{ $filters.howMuchTime(paciente.data_inicio_tratamento, { type: 'date' }) }}</div>
        </div>
      </div>

      <!-- 2 - Término previsto -->
      <div class="col-6 col-md mb-2 mb-md-0">
        <div class="stats-card">
          <div class="stats-title">
            <font-awesome-icon :icon="['fas', 'stopwatch']" class="me-1" />
            Término previsto
          </div>
          <div class="date-badge-container">
            <div class="date-badge">
              {{ this.$filters.dateDmy(paciente.data_final_prevista) }}
            </div>
            <div class="edit-icon" @click="editarDataFinal">
              <font-awesome-icon :icon="['fas', 'edit']" />
            </div>
          </div>
          <div class="text-sm">{{ $filters.howMuchTime(paciente.data_final_prevista, { type: 'date' }) }}</div>
        </div>
      </div>

      <!-- 3 - Próxima consulta -->
      <div class="col-6 col-md mb-2 mb-md-0">
        <div class="stats-card proxima-consulta-card">
          <div class="stats-title">
            <font-awesome-icon :icon="['fas', 'calendar-check']" class="me-1" />
            Próxima consulta
          </div>
          <div class="date-badge">
            {{ proximaConsulta ? $filters.dateDmy(proximaConsulta) : '-' }}
          </div>
          <div class="text-sm" v-if="proximaConsulta">{{ $filters.howMuchTime(proximaConsulta, { type: 'date' }) }}</div>
          <div v-if="!proximaConsulta" class="mt-2">
            <button
              class="btn btn-sm btn-outline-primary agendar-btn"
              @click="abrirModalNovaConsulta"
              style="padding: 2px 10px;"
            >
              Agendar
            </button>
          </div>
        </div>
      </div>

      <!-- 4 - Metas concluídas -->
      <div class="col-6 col-md mb-2 mb-md-0" v-if="paciente.status_tratamento == 'ATIVO'">
        <div class="stats-card">
          <div class="stats-title">
            <font-awesome-icon :icon="['fas', 'check-double']" class="me-1" />
            Metas concluídas
          </div>
          <div class="d-flex flex-column align-items-center justify-content-center w-100 p-0 m-0 mb-1">
            <div class="progress progress-lg w-100">
              <div class="progress-container" :style="{ width: getMetasProgresso + '%' }">
                <div
                  class="progress-bar bg-gradient-success"
                  role="progressbar"
                  style="width: 100%"
                  :aria-valuenow="getMetasProgresso"
                  aria-valuemin="0"
                  aria-valuemax="100"
                ></div>
              </div>
              <span class="progress-value">{{ getMetasConcluidas }} de {{ getTotalMetas }}</span>
            </div>
          </div>
          <span class="text-sm">Restam {{ getMetasRestantes }} metas</span>
        </div>
      </div>

      <!-- 5 - Progresso geral -->
      <div class="col-6 col-md mb-2 mb-md-0" v-if="paciente.status_tratamento == 'ATIVO'">
        <div class="stats-card">
          <div class="stats-title">
            <font-awesome-icon :icon="['fas', 'chart-line']" class="me-1" />
            Progresso geral
          </div>
          <div class="d-flex flex-column align-items-center justify-content-center w-100 p-0 m-0 mb-1">
            <div class="progress progress-lg w-100">
              <div class="progress-container" :style="{ width: getProgresso + '%' }">
                <div
                  class="progress-bar bg-gradient-success"
                  role="progressbar"
                  style="width: 100%"
                  :aria-valuenow="getProgresso"
                  aria-valuemin="0"
                  aria-valuemax="100"
                ></div>
              </div>
              <span class="progress-value">{{ getProgresso }}%</span>
            </div>
          </div>
          <span class="text-sm">{{ getDuracaoMeses }} meses</span>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="w-100 text-center py-3">
    <div class="spinner-border text-primary" role="status"></div>
  </div>
</template>

<script>
import cSwal from "@/utils/cSwal.js";
import { updatePacienteField } from "@/services/pacientesService";

export default {
  name: "ConsultasStats",
  props: {
    paciente: {
      type: Object,
      required: true
    }
  },
  emits: ['pacienteChange', 'abrirModalConsulta'],
  data() {
    return {
      isLoading: false
    };
  },
  computed: {
    // Cálculo do progresso geral do tratamento
    getProgresso() {
      if (!this.paciente || !this.paciente.data_inicio_tratamento || !this.paciente.data_final_prevista) {
        return 0;
      }

      const dataInicio = new Date(this.paciente.data_inicio_tratamento);
      const dataFinal = new Date(this.paciente.data_final_prevista);
      const hoje = new Date();

      // Se o tratamento ainda não começou
      if (hoje < dataInicio) return 0;

      // Se o tratamento já terminou
      if (hoje > dataFinal) return 100;

      // Calcular o progresso
      const totalDias = (dataFinal - dataInicio) / (1000 * 60 * 60 * 24);
      const diasPassados = (hoje - dataInicio) / (1000 * 60 * 60 * 24);

      const progresso = (diasPassados / totalDias) * 100;
      return parseFloat(progresso.toFixed(1));
    },

    // Total de metas terapêuticas
    getTotalMetas() {
      if (!this.paciente || !this.paciente.metas_terapeuticas) return 0;
      return this.paciente.metas_terapeuticas.length;
    },

    // Metas terapêuticas concluídas
    getMetasConcluidas() {
      if (!this.paciente || !this.paciente.metas_terapeuticas) return 0;
      return this.paciente.metas_terapeuticas.filter(meta => meta.status === 'CONCLUIDA').length;
    },

    // Metas terapêuticas restantes
    getMetasRestantes() {
      if (!this.paciente || !this.paciente.metas_terapeuticas) return 0;
      return this.getTotalMetas - this.getMetasConcluidas;
    },

    // Progresso das metas terapêuticas
    getMetasProgresso() {
      if (!this.paciente || !this.paciente.metas_terapeuticas || this.getTotalMetas === 0) return 0;
      const progresso = (this.getMetasConcluidas / this.getTotalMetas) * 100;
      return parseFloat(progresso.toFixed(1));
    },

    // Duração do tratamento em meses
    getDuracaoMeses() {
      if (!this.paciente || !this.paciente.data_inicio_tratamento || !this.paciente.data_final_prevista) {
        return "0 de 0";
      }

      const dataInicio = new Date(this.paciente.data_inicio_tratamento);
      const dataFinal = new Date(this.paciente.data_final_prevista);
      const hoje = new Date();

      // Calcular o total de meses entre início e fim
      const totalMeses = (dataFinal.getFullYear() - dataInicio.getFullYear()) * 12 +
                         (dataFinal.getMonth() - dataInicio.getMonth());

      // Calcular quantos meses já se passaram
      const mesesPassados = (hoje.getFullYear() - dataInicio.getFullYear()) * 12 +
                           (hoje.getMonth() - dataInicio.getMonth());

      const mesesDecorridos = Math.max(0, Math.min(mesesPassados, totalMeses));

      return `${mesesDecorridos} de ${totalMeses}`;
    },

    // Data da próxima consulta (consultas futuras)
    proximaConsulta() {
      if (!this.paciente.consultas || !Array.isArray(this.paciente.consultas) || this.paciente.consultas.length === 0) {
        return null;
      }

      const agora = new Date();

      const consultasFuturas = this.paciente.consultas
        .filter(consulta => {
          const dataConsulta = new Date(consulta.horario);
          // Considera consultas a partir do momento atual (incluindo hoje)
          return dataConsulta >= agora && consulta.status !== 'cancelada';
        })
        .sort((a, b) => new Date(a.horario) - new Date(b.horario));

      return consultasFuturas.length > 0 ? consultasFuturas[0].horario : null;
    }
  },
  methods: {
    statusClass(status) {
      const classMap = {
        "NÃO INICIADO": "bg-gradient-warning",
        CONCLUÍDO: "bg-gradient-success",
        ATIVO: "bg-gradient-secondary",
      };

      return classMap[status] || "";
    },
    statusText(status) {
      const textMap = {
        "NÃO INICIADO": "NÃO INICIADO",
        CONCLUÍDO: "CONCLUÍDO",
        ATIVO: "EM ANDAMENTO",
      };

      return textMap[status] || "";
    },
    editarDataInicio() {
      const dataAtual = this.paciente.data_inicio_tratamento ? this.paciente.data_inicio_tratamento.split('T')[0] : '';
      const dataFormatada = this.$filters.dateDmy(this.paciente.data_inicio_tratamento);

      cSwal.fire({
        title: 'Editar data de início',
        html: `<p>Alterar a data de início do tratamento:</p>
               <p>Data atual: <b>${dataFormatada}</b></p>`,
        input: 'date',
        inputValue: dataAtual,
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        inputValidator: (value) => {
          if (!value) {
            return 'É necessário selecionar uma data';
          }
        }
      }).then((result) => {
        if (result.isConfirmed && result.value) {
          this.atualizarCampoPaciente('data_inicio_tratamento', result.value);
        }
      });
    },
    editarDataFinal() {
      const dataAtual = this.paciente.data_final_prevista ? this.paciente.data_final_prevista.split('T')[0] : '';
      const dataFormatada = this.$filters.dateDmy(this.paciente.data_final_prevista);

      cSwal.fire({
        title: 'Editar data de término',
        html: `<p>Alterar a data de término previsto do tratamento:</p>
               <p>Data atual: <b>${dataFormatada}</b></p>`,
        input: 'date',
        inputValue: dataAtual,
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        inputValidator: (value) => {
          if (!value) {
            return 'É necessário selecionar uma data';
          }
        }
      }).then((result) => {
        if (result.isConfirmed && result.value) {
          this.atualizarCampoPaciente('data_final_prevista', result.value);
        }
      });
    },
    async atualizarCampoPaciente(campo, valor) {
      try {
        this.isLoading = true;

        // Confirmar a atualização
        cSwal.cConfirm(
          `Deseja realmente alterar a data para <b>${this.$filters.dateDmy(valor)}</b>?`,
          async () => {
            cSwal.loading('Atualizando dados do paciente...');

            const resultado = await updatePacienteField(this.paciente.id, campo, valor);

            cSwal.loaded();

            if (resultado) {
              cSwal.cSuccess('Data atualizada com sucesso!');

              // Emitir evento para atualizar os dados do paciente no componente pai
              this.$emit('pacienteChange');
            } else {
              cSwal.cError('Erro ao atualizar a data. Por favor, tente novamente.');
            }

            this.isLoading = false;
          },
          {
            title: 'Confirmar alteração',
            confirmButtonText: 'Sim, alterar',
            cancelButtonText: 'Cancelar'
          }
        );
      } catch (error) {
        console.error('Erro ao atualizar campo do paciente:', error);
        cSwal.cError('Erro ao processar a solicitação. Por favor, tente novamente.');
        this.isLoading = false;
      }
    },
    abrirModalNovaConsulta() {
      this.$emit('abrirModalConsulta');
    }
  }
};
</script>

<style scoped>
/* Estilos para os cards de estatísticas */
.consultas-stats {
  margin-top: 0.5rem;
}

.stats-card {
  background-color: #fff;
  border-radius: 6px;
  padding: 0.6rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  justify-content: space-around;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.7rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.3rem;
  letter-spacing: 0.4px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #344767;
  margin-bottom: 0.25rem;
}

.stats-subtitle {
  font-size: 0.75rem;
  color: #67748e;
  font-weight: 500;
}

/* Estilos para o date-badge */
.date-badge-container {
  position: relative;
  width: 100%;
  margin-bottom: 0.2rem;
}

.date-badge {
  width: 100%;
  height: var(--lumi-input-height, 30px);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f3f4f6;
  border: 1px solid #e2e2e6;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  font-weight: 500;
  color: #495057;
  line-height: 1.1;
  padding: 0 0.6rem;
  font-size: 0.85rem;
}

.date-badge:hover {
  background-color: #f0f0f2;
  border-color: #d8d8dc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.edit-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.2s ease, color 0.2s ease;
  cursor: pointer;
  font-size: 0.8rem;
  color: #adb5bd;
  z-index: 2;
}

.date-badge-container:hover .edit-icon {
  opacity: 1;
}

.edit-icon:hover {
  color: #495057;
}

/* Estilos para as barras de progresso */
.progress {
  position: relative;
  height: 6px;
  border-radius: 0.4rem;
  background-color: #e9ecef;
  overflow: visible;
}

.progress-lg {
  height: 6px;
}

.progress-container {
  height: 100%;
  transition: width 0.6s ease;
  position: relative;
}

.progress-bar {
  height: 100%;
  border-radius: 0.5rem;
  transition: width 0.6s ease;
  position: relative;
}

.bg-gradient-success {
  background: linear-gradient(310deg, #17ad37, #7fce18);
}

.progress-value {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.65rem;
  font-weight: 600;
  color: #344767;
  z-index: 2;
  white-space: nowrap;
  text-shadow: 0px 0px 2px white;
}

.text-sm {
  font-size: 0.65rem;
  color: #67748e;
}

/* Estilos para o status badge */
.status-badge {
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-radius: 0.5rem;
  color: white;
  display: inline-block;
  margin-top: 0.4rem;
}

.bg-gradient-warning {
  background: linear-gradient(310deg, #f53939, #fbcf33);
}

.bg-gradient-secondary {
  background: linear-gradient(310deg, #627594, #a8b8d8);
}

.badge-sm {
  font-size: 0.65rem;
  padding: 0.35em 0.65em;
}

/* Estilos para o botão de agendar */
.agendar-btn {
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #0d6efd;
  color: #0d6efd;
  background-color: transparent;
}

.agendar-btn:hover {
  background-color: #0d6efd;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
}

/* Destaque sutil para o card de próxima consulta */
.proxima-consulta-card {
  border: 1px solid rgba(26, 80, 161, 0.05) !important;
  box-shadow: 0 2px 12px rgba(13, 110, 253, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05) !important;
  background: linear-gradient(135deg, #ffffff 0%, rgba(13, 110, 253, 0.02) 100%);
}

.proxima-consulta-card:hover {
  border: 1px solid rgba(26, 80, 161, 0.1) !important;
  box-shadow: 0 4px 20px rgba(13, 110, 253, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08) !important;
}
</style>
